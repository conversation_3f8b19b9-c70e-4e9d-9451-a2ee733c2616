from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Any
from jobspy import scrape_jobs

app = FastAPI()

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://530dc06a5900.ngrok-free.app",
    "*"  # Allow all origins for development
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class LinkedInJobSearchRequest(BaseModel):
    search_term: Optional[str] = None
    location: Optional[str] = None
    results_wanted: Optional[int] = 5
    hours_old: Optional[int] = 72
    job_type: Optional[str] = None
    is_remote: Optional[bool] = None
    easy_apply: Optional[bool] = None
    offset: Optional[int] = 0
    verbose: Optional[int] = 2
    linkedin_fetch_description: Optional[bool] = None
    proxies: Optional[List[str]] = None

def run_linkedin_scraper(params: dict) -> Any:
    try:
        params["site_name"] = ["linkedin"]
        jobs = scrape_jobs(**params)
        if jobs.empty:
            raise HTTPException(status_code=404, detail="No LinkedIn jobs found.")
        cleaned_jobs = jobs.replace([float("inf"), float("-inf")], None).fillna("")
        job_list = cleaned_jobs.to_dict(orient="records")
        # Fields to remove from each job dict (using correct keys with underscores)
        fields_to_remove = [
            "salary_source", "interval", "min_amount", "max_amount", "currency", "listing_type", "emails", "company_url_direct", "company_addresses", "company_num_employees", "company_revenue", "company_description", "skills", "experience_range", "company_rating", "company_reviews_count", "vacancy_count", "work_from_home_type"
        ]
        for job in job_list:
            for field in fields_to_remove:
                job.pop(field, None)
        return {"message": f"Found {len(job_list)} LinkedIn jobs", "jobs": job_list}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scrape-linkedin/")
async def scrape_linkedin(request: LinkedInJobSearchRequest = Body(...)):
    params = request.dict(exclude_none=True)
    return run_linkedin_scraper(params)

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "LinkedIn Job Scraper (jobspy)",
        "endpoints": [
            "/scrape-linkedin/"
        ]
    } 